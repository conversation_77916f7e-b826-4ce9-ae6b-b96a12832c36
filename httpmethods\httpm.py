from fastapi import <PERSON><PERSON><PERSON>, HTTPException, status, Depends, Query
from fastapi.responses import JSONResponse
from pydantic import BaseModel, EmailStr
from typing import Optional, List
from datetime import datetime
import uvicorn

# Initialize FastAPI app
app = FastAPI(
    title="User Management API",
    description="A complete FastAPI application for user management",
    version="1.0.0"
)

# Pydantic models
class User(BaseModel):
    name: str
    email: EmailStr
    age: int
    is_active: bool = True

class UserUpdate(BaseModel):
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    age: Optional[int] = None
    is_active: Optional[bool] = None

class UserResponse(BaseModel):
    id: int
    name: str
    email: str
    age: int
    is_active: bool
    created_at: datetime

# In-memory database (in production, use a real database)
fake_users_db = {}
user_id_counter = 1

# Dependency to get user by ID
def get_user_by_id(user_id: int):
    if user_id not in fake_users_db:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"User with id {user_id} not found"
        )
    return fake_users_db[user_id]

# Root endpoint
@app.get("/")
async def root():
    return {
        "message": "Welcome to User Management API",
        "docs": "/docs",
        "total_users": len(fake_users_db)
    }

# Health check endpoint
@app.get("/health")
async def health_check():
    return {
        "status": "healthy",
        "timestamp": datetime.now(),
        "version": "1.0.0"
    }

# GET - Retrieve all users with pagination
@app.get("/users", response_model=List[UserResponse])
async def get_users(
    skip: int = Query(0, ge=0, description="Number of users to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of users to return"),
    is_active: Optional[bool] = Query(None, description="Filter by active status")
):
    users = list(fake_users_db.values())
    
    # Filter by active status if provided
    if is_active is not None:
        users = [user for user in users if user["is_active"] == is_active]
    
    # Apply pagination
    users = users[skip:skip + limit]
    
    return users

# GET - Retrieve a specific user
@app.get("/users/{user_id}", response_model=UserResponse)
async def get_user(user_id: int):
    user = get_user_by_id(user_id)
    return user

# POST - Create a new user
@app.post("/users", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(user: User):
    global user_id_counter
    
    # Check if email already exists
    for existing_user in fake_users_db.values():
        if existing_user["email"] == user.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
    
    # Create new user
    new_user = {
        "id": user_id_counter,
        "name": user.name,
        "email": user.email,
        "age": user.age,
        "is_active": user.is_active,
        "created_at": datetime.now()
    }
    
    fake_users_db[user_id_counter] = new_user
    user_id_counter += 1
    
    return new_user

# PUT - Update entire user (replace)
@app.put("/users/{user_id}", response_model=UserResponse)
async def update_user(user_id: int, user: User):
    existing_user = get_user_by_id(user_id)
    
    # Check if email is being changed to an existing email
    for uid, existing in fake_users_db.items():
        if uid != user_id and existing["email"] == user.email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email already registered"
            )
    
    # Update user (complete replacement)
    updated_user = {
        "id": user_id,
        "name": user.name,
        "email": user.email,
        "age": user.age,
        "is_active": user.is_active,
        "created_at": existing_user["created_at"]  # Keep original creation time
    }
    
    fake_users_db[user_id] = updated_user
    return updated_user

# PATCH - Partially update user
@app.patch("/users/{user_id}", response_model=UserResponse)
async def patch_user(user_id: int, user_update: UserUpdate):
    existing_user = get_user_by_id(user_id)
    
    # Check if email is being changed to an existing email
    if user_update.email:
        for uid, existing in fake_users_db.items():
            if uid != user_id and existing["email"] == user_update.email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email already registered"
                )
    
    # Update only provided fields
    update_data = user_update.dict(exclude_unset=True)
    for field, value in update_data.items():
        existing_user[field] = value
    
    fake_users_db[user_id] = existing_user
    return existing_user

# DELETE - Remove a user
@app.delete("/users/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(user_id: int):
    get_user_by_id(user_id)  # Check if user exists
    del fake_users_db[user_id]
    return None

# HEAD - Check if user exists (returns headers only)
@app.head("/users/{user_id}")
async def head_user(user_id: int):
    get_user_by_id(user_id)  # Will raise 404 if not found
    return None

# OPTIONS - Get allowed methods
@app.options("/users")
async def options_users():
    return {
        "allowed_methods": ["GET", "POST"],
        "description": "Users collection endpoint"
    }

@app.options("/users/{user_id}")
async def options_user(user_id: int):
    return {
        "allowed_methods": ["GET", "PUT", "PATCH", "DELETE", "HEAD"],
        "description": "Individual user endpoint"
    }

# Search users
@app.get("/users/search/", response_model=List[UserResponse])
async def search_users(
    name: Optional[str] = Query(None, description="Search by name"),
    min_age: Optional[int] = Query(None, ge=0, description="Minimum age"),
    max_age: Optional[int] = Query(None, le=150, description="Maximum age")
):
    users = list(fake_users_db.values())
    
    if name:
        users = [user for user in users if name.lower() in user["name"].lower()]
    
    if min_age is not None:
        users = [user for user in users if user["age"] >= min_age]
    
    if max_age is not None:
        users = [user for user in users if user["age"] <= max_age]
    
    return users

# Get user statistics
@app.get("/users/stats")
async def get_user_stats():
    users = list(fake_users_db.values())
    
    if not users:
        return {
            "total_users": 0,
            "active_users": 0,
            "inactive_users": 0,
            "average_age": 0
        }
    
    active_count = sum(1 for user in users if user["is_active"])
    ages = [user["age"] for user in users]
    
    return {
        "total_users": len(users),
        "active_users": active_count,
        "inactive_users": len(users) - active_count,
        "average_age": round(sum(ages) / len(ages), 2)
    }

# Bulk operations
@app.post("/users/bulk", response_model=List[UserResponse], status_code=status.HTTP_201_CREATED)
async def create_users_bulk(users: List[User]):
    global user_id_counter
    created_users = []
    
    # Check for duplicate emails in the batch
    emails_in_batch = [user.email for user in users]
    if len(emails_in_batch) != len(set(emails_in_batch)):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Duplicate emails in batch"
        )
    
    # Check against existing users
    existing_emails = {user["email"] for user in fake_users_db.values()}
    for user in users:
        if user.email in existing_emails:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Email {user.email} already registered"
            )
    
    # Create all users
    for user in users:
        new_user = {
            "id": user_id_counter,
            "name": user.name,
            "email": user.email,
            "age": user.age,
            "is_active": user.is_active,
            "created_at": datetime.now()
        }
        fake_users_db[user_id_counter] = new_user
        created_users.append(new_user)
        user_id_counter += 1
    
    return created_users

# Exception handlers
@app.exception_handler(ValueError)
async def value_error_handler(request, exc):
    return JSONResponse(
        status_code=status.HTTP_400_BAD_REQUEST,
        content={"detail": str(exc)}
    )

# Add some sample data on startup
@app.on_event("startup")
async def startup_event():
    global user_id_counter
    sample_users = [
        {
            "id": 1,
            "name": "John Doe",
            "email": "<EMAIL>",
            "age": 30,
            "is_active": True,
            "created_at": datetime.now()
        },
        {
            "id": 2,
            "name": "Jane Smith",
            "email": "<EMAIL>",
            "age": 25,
            "is_active": True,
            "created_at": datetime.now()
        }
    ]
    
    for user in sample_users:
        fake_users_db[user["id"]] = user
    
    user_id_counter = len(sample_users) + 1

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8000)